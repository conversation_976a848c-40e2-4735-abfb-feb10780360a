<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>知识点复习</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f7fafc; }
        .container { max-width: 540px; margin: 40px auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 16px #0002; padding: 36px 32px; }
        h2 { text-align: center; margin-bottom: 24px; }
        .question { font-size: 1.15rem; margin-bottom: 18px; font-weight: 600; }
        .options { margin-bottom: 18px; }
        .options label { display: block; margin: 8px 0; font-weight: 400; }
        .answer-area { margin-bottom: 18px; }
        .msg { min-height: 28px; color: #f5365c; text-align: center; margin-top: 12px; font-size: 1.08rem; }
        .success { color: #2dce89; }
        button { width: 100%; padding: 12px; background: #5e72e4; color: #fff; border: none; border-radius: 6px; font-size: 1.1rem; font-weight: 600; cursor: pointer; margin-top: 10px; }
        button:hover { background: #2dce89; }
        .progress { text-align: right; color: #888; font-size: 0.98rem; margin-bottom: 10px; }
        .mastery { color: #5e72e4; font-weight: bold; }
    </style>
</head>
<body>
<div class="container">
    <h2>知识点复习</h2>
    <div id="dailyCountContainer" style="text-align:center;margin:16px 0;">
        今日复习题数：
        <select id="dailyCountSelect">
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="20">20</option>
        </select>
        <button id="setDailyBtn">确定</button>
    </div>
    <div class="progress" id="progress"></div>
    <div id="review-area"></div>
    <div class="msg" id="msg"></div>
    <div id="all-knowledge-table" style="margin-top:36px;"></div>
</div>
<script>
let user_id = localStorage.getItem('user_id') || 1;
let allCandidates = [];
let reviewQueue = [];
let dailyCount = 5; // 默认每天5道

function renderAllKnowledgeTable() {
    fetch(`/all_knowledge?user_id=${user_id}`)
        .then(res => res.json())
        .then(data => {
            let html = `<table style='width:100%;border-collapse:collapse;margin-top:18px;font-size:0.98rem;'>` +
                `<tr style='background:#f1f3f6;'><th>内容</th><th>题型</th><th>难度</th><th>掌握度</th><th>复习次数</th><th>正确次数</th><th>最后复习</th></tr>`;
            data.forEach(kp => {
                html += `<tr style='text-align:center;border-bottom:1px solid #eee;'>` +
                    `<td style='max-width:180px;word-break:break-all;'>${kp.content}</td>` +
                    `<td>${typeLabel(kp.type)}</td>` +
                    `<td>${kp.difficulty}</td>` +
                    `<td>${(kp.mastery*100).toFixed(0)}%</td>` +
                    `<td>${kp.review_times}</td>` +
                    `<td>${kp.correct_reviews}</td>` +
                    `<td>${kp.last_study_time ? kp.last_study_time.replace('T',' ').slice(0,16) : '-'}</td>` +
                    `</tr>`;
            });
            html += `</table>`;
            document.getElementById('all-knowledge-table').innerHTML = html;
        });
}

window.onload = function() {
    fetch(`/study/candidates?user_id=${user_id}`)
        .then(res => res.json())
        .then(data => {
            allCandidates = data;
            dailyCount = parseInt(document.getElementById('dailyCountSelect').value);
            resetReviewQueue();
            showCandidate();
            renderAllKnowledgeTable();
        });
    document.getElementById('dailyCountSelect').onchange = function() {
        dailyCount = parseInt(this.value);
        resetReviewQueue();
        showCandidate();
        document.getElementById('dailyCountContainer').style.display = 'none';
    };
    document.getElementById('setDailyBtn').onclick = function() {
        dailyCount = parseInt(document.getElementById('dailyCountSelect').value);
        resetReviewQueue();
        showCandidate();
        document.getElementById('dailyCountContainer').style.display = 'none';
    };
};

function resetReviewQueue() {
    reviewQueue = allCandidates.slice(0, dailyCount).map(kp => ({...kp}));
}

function showCandidate() {
    const area = document.getElementById('review-area');
    const msg = document.getElementById('msg');
    const progress = document.getElementById('progress');
    msg.textContent = '';
    if (reviewQueue.length === 0) {
        area.innerHTML = '<div style="text-align:center;font-size:1.2rem;color:#2dce89;">今日复习完成！<br><button id="nextDayBtn" style="margin-top:18px;background:#5e72e4;">继续下一天</button></div>';
        progress.textContent = '';
        document.getElementById('dailyCountContainer').style.display = 'block';
        document.getElementById('nextDayBtn').onclick = function() {
            loadCandidates();
        };
        return;
    }
    progress.textContent = `进度：${dailyCount - reviewQueue.length + 1} / ${dailyCount}`;
    const kp = reviewQueue[0];
    let html = `<div class="question">${kp.content}</div>`;
    html += `<div style='margin-bottom:8px;'>题型：<b>${typeLabel(kp.type)}</b> &nbsp; 掌握度：<span class='mastery'>${(kp.mastery*100).toFixed(0)}%</span></div>`;
    if (kp.type === 'recite' || kp.type === 'memory') {
        html += `<div style="text-align:center;margin:24px 0;">
                    <button id="rememberBtn" style="width:45%;margin-right:5%;background:#2dce89;">记住了</button>
                    <button id="skipBtn" style="width:45%;background:#888;">没记住</button>
                 </div>`;
        area.innerHTML = html;
        document.getElementById('rememberBtn').onclick = function() {
            // 记住了，移除队首
            fetch('/study/review', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    user_id: user_id,
                    knowledge_point_id: kp.id,
                    user_answer: '',
                    reaction_time_seconds: 2.0
                })
            }).then(res => res.json()).then(resp => {
                msg.textContent = '已记住！';
                msg.className = 'msg success';
                reviewQueue.shift();
                setTimeout(() => {
                    showCandidate();
                }, 800);
            });
        };
        document.getElementById('skipBtn').onclick = function() {
            // 没记住，放到队尾
            reviewQueue.push(reviewQueue.shift());
            showCandidate();
        };
        return;
    }
    html += `<form id='answerForm' onsubmit='return submitAnswer(event)'>`;
    // 动态渲染答题区
    if (kp.type === 'single') {
        html += '<div class="options">';
        (kp.options ? kp.options.split(',') : []).forEach((opt, i) => {
            html += `<label><input type='radio' name='user_answer' value='${opt}' required> ${opt}</label>`;
        });
        html += '</div>';
    } else if (kp.type === 'multi') {
        html += '<div class="options">';
        (kp.options ? kp.options.split(',') : []).forEach((opt, i) => {
            html += `<label><input type='checkbox' name='user_answer' value='${opt}'> ${opt}</label>`;
        });
        html += '<div style="color:#888;font-size:0.95rem;">（可多选）</div></div>';
    } else if (kp.type === 'judge') {
        html += `<div class="options">
            <label><input type='radio' name='user_answer' value='true' required> 正确</label>
            <label><input type='radio' name='user_answer' value='false'> 错误</label>
        </div>`;
    } else if (kp.type === 'fill') {
        html += `<div class="answer-area"><input type='text' name='user_answer' placeholder='请填写答案' required style='width:100%;padding:8px;'></div>`;
    } else if (kp.type === 'short') {
        html += `<div class="answer-area"><textarea name='user_answer' rows='3' placeholder='请简要作答' required style='width:100%;padding:8px;'></textarea></div>`;
    } else if (kp.type === 'recite') {
        html += `<div class="answer-area"><textarea name='user_answer' rows='3' placeholder='请默写/背诵内容' required style='width:100%;padding:8px;'></textarea></div>`;
    } else {
        html += `<div class="answer-area"><input type='text' name='user_answer' placeholder='请填写答案' required style='width:100%;padding:8px;'></div>`;
    }
    html += `<button type='submit'>提交答案</button>`;
    html += `<button type='button' id='skipBtn' style='margin-top:10px;background:#888;'>不会</button>`;
    html += '</form>';
    area.innerHTML = html;
    document.getElementById('skipBtn').onclick = function() {
        // 不会，放到队尾
        reviewQueue.push(reviewQueue.shift());
        showCandidate();
    };
}

function typeLabel(type) {
    switch(type) {
        case 'single': return '单选题';
        case 'multi': return '多选题';
        case 'judge': return '判断题';
        case 'fill': return '填空题';
        case 'short': return '简答题';
        case 'recite': return '默写/背诵题';
        default: return type;
    }
}

function submitAnswer(event) {
    event.preventDefault();
    const kp = reviewQueue[0];
    let user_answer = '';
    if (kp.type === 'multi') {
        // 多选题收集所有选中的值，逗号拼接
        user_answer = Array.from(document.querySelectorAll('input[name="user_answer"]:checked')).map(i=>i.value).join(',');
        if (!user_answer) {
            document.getElementById('msg').textContent = '请至少选择一个答案';
            return false;
        }
    } else if (kp.type === 'single' || kp.type === 'judge') {
        const checked = document.querySelector('input[name="user_answer"]:checked');
        if (!checked) {
            document.getElementById('msg').textContent = '请选择答案';
            return false;
        }
        user_answer = checked.value;
    } else {
        user_answer = document.querySelector('[name="user_answer"]').value.trim();
        if (!user_answer) {
            document.getElementById('msg').textContent = '请填写答案';
            return false;
        }
    }
    fetch('/study/review', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            user_id: user_id,
            knowledge_point_id: kp.id,
            user_answer: user_answer,
            reaction_time_seconds: 2.0
        })
    }).then(res => res.json()).then(resp => {
        if (resp.is_correct) {
            document.getElementById('msg').textContent = '回答正确！';
            document.getElementById('msg').className = 'msg success';
            reviewQueue.shift();
        } else {
            document.getElementById('msg').textContent = '回答错误！';
            document.getElementById('msg').className = 'msg';
            // 答错，放到队尾
            reviewQueue.push(reviewQueue.shift());
        }
        setTimeout(() => {
            showCandidate();
        }, 1200);
    });
    return false;
}

function loadCandidates() {
    fetch(`/study/candidates?user_id=${user_id}`)
        .then(res => res.json())
        .then(data => {
            allCandidates = data;
            dailyCount = parseInt(document.getElementById('dailyCountSelect').value);
            resetReviewQueue();
            showCandidate();
            document.getElementById('dailyCountContainer').style.display = 'none';
        });
}

</script>
</body>
</html>