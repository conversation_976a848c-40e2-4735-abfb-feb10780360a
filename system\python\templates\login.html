<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录/注册</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', Arial, sans-serif;
            background: linear-gradient(120deg, #5e72e4 0%, #2dce89 100%);
            min-height: 100vh;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
            padding: 36px 32px 28px 32px;
            width: 350px;
            max-width: 90vw;
        }
        h2 {
            text-align: center;
            margin-bottom: 18px;
            color: #32325d;
        }
        .tab {
            display: flex;
            justify-content: center;
            margin-bottom: 18px;
        }
        .tab button {
            background: none;
            border: none;
            font-size: 1.1rem;
            font-weight: 600;
            color: #5e72e4;
            margin: 0 12px;
            padding: 6px 0;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            transition: border 0.2s;
        }
        .tab button.active {
            border-bottom: 2px solid #5e72e4;
            color: #2dce89;
        }
        form {
            display: flex;
            flex-direction: column;
        }
        input[type="text"], input[type="password"] {
            padding: 10px;
            margin-bottom: 16px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            font-size: 1rem;
        }
        button[type="submit"] {
            background: #5e72e4;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            margin-top: 6px;
            transition: background 0.2s;
        }
        button[type="submit"]:hover {
            background: #2dce89;
        }
        .msg {
            min-height: 24px;
            text-align: center;
            color: #f5365c;
            margin-bottom: 8px;
        }
        .success {
            color: #2dce89;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 id="form-title">登录</h2>
        <div class="tab">
            <button id="login-tab" class="active">登录</button>
            <button id="register-tab">注册</button>
        </div>
        <div class="msg" id="msg"></div>
        <form id="login-form">
            <input type="text" id="login-username" placeholder="用户名" required autocomplete="username">
            <input type="password" id="login-password" placeholder="密码" required autocomplete="current-password">
            <button type="submit">登录</button>
        </form>
        <form id="register-form" style="display:none;">
            <input type="text" id="register-username" placeholder="用户名" required autocomplete="username">
            <input type="password" id="register-password" placeholder="密码" required autocomplete="new-password">
            <button type="submit">注册</button>
        </form>
    </div>
    <script>
        const loginTab = document.getElementById('login-tab');
        const registerTab = document.getElementById('register-tab');
        const loginForm = document.getElementById('login-form');
        const registerForm = document.getElementById('register-form');
        const msg = document.getElementById('msg');
        const formTitle = document.getElementById('form-title');

        loginTab.onclick = function() {
            loginTab.classList.add('active');
            registerTab.classList.remove('active');
            loginForm.style.display = '';
            registerForm.style.display = 'none';
            formTitle.textContent = '登录';
            msg.textContent = '';
        };
        registerTab.onclick = function() {
            registerTab.classList.add('active');
            loginTab.classList.remove('active');
            loginForm.style.display = 'none';
            registerForm.style.display = '';
            formTitle.textContent = '注册';
            msg.textContent = '';
        };

        loginForm.onsubmit = async function(e) {
            e.preventDefault();
            msg.textContent = '登录中...';
            msg.className = 'msg';
            const username = document.getElementById('login-username').value.trim();
            const password = document.getElementById('login-password').value;
            try {
                const res = await fetch('/user/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
                const data = await res.json();
                if (res.ok && data.status === 'success') {
                    msg.textContent = '登录成功，正在跳转...';
                    msg.className = 'msg success';
                    setTimeout(() => {
                        window.location.href = '/static/knowledge_submit.html';
                    }, 800);
                } else {
                    msg.textContent = data.detail || data.message || '登录失败';
                }
            } catch (err) {
                msg.textContent = '网络错误';
            }
        };

        registerForm.onsubmit = async function(e) {
            e.preventDefault();
            msg.textContent = '注册中...';
            msg.className = 'msg';
            const username = document.getElementById('register-username').value.trim();
            const password = document.getElementById('register-password').value;
            try {
                const res = await fetch('/user/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
                const data = await res.json();
                if (res.ok && data.status === 'success') {
                    msg.textContent = '注册成功，请登录';
                    msg.className = 'msg success';
                    setTimeout(() => {
                        loginTab.click();
                    }, 800);
                } else {
                    msg.textContent = data.detail || data.message || '注册失败';
                }
            } catch (err) {
                msg.textContent = '网络错误';
            }
        };
    </script>
</body>
</html> 