from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Optional, List
from sqlalchemy.orm import Session
from python.service.models import SessionLocal, KnowledgePoint
from python.service.knowledge_service import create_knowledge_point

from python.utils.logger import get_logger

router = APIRouter()
logger = get_logger(__name__)

class KnowledgeSubmit(BaseModel):
    content: str
    difficulty: float
    type: str
    options: Optional[str] = None
    answer: Optional[str] = None  # 允许为空

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.post("/submit")
def submit_knowledge(data: KnowledgeSubmit, db: Session = Depends(get_db)):
    # 记忆题不需要答案
    if data.type not in ['recite', 'memory'] and not data.answer:
        logger.error(f"知识点提交失败: 缺少标准答案, data={data}")
        raise HTTPException(status_code=400, detail="该题型必须填写标准答案")
    try:
        create_knowledge_point(db, data.content, data.difficulty, data.type, data.options, data.answer)
    except Exception as e:
        logger.error(f"知识点提交异常: {e}, data={data}")
        raise HTTPException(status_code=500, detail="知识点提交异常")
    return {"status": "success", "message": "知识点提交成功"}

@router.get("/all")
def get_all_knowledge(db: Session = Depends(get_db)):
    kps = db.query(KnowledgePoint).all()
    return [
        {
            "id": kp.id,
            "content": kp.content,
            "type": kp.type,
            "difficulty": kp.difficulty
        } for kp in kps
    ]

