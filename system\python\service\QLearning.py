import numpy as np
import pandas as pd
import random
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from python.utils.logger import get_logger

logger = get_logger(__name__)

class EbbinghausForgettingCurve:
    """艾宾浩斯遗忘曲线模型，基于时间间隔计算知识点的遗忘概率并推荐最优复习时间

    该模型使用简化的指数函数模拟人类记忆的遗忘过程，并根据当前掌握程度
    动态计算最佳复习时机，平衡记忆保留和学习效率。
    """

    def __init__(self, alpha: float = 0.8, beta: float = 0.5):
        """
        初始化遗忘曲线参数

        Args:
            alpha (float): 遗忘速度参数，取值范围(0, 1]，值越大遗忘速度越快，默认0.8
            beta (float): 记忆保持参数，取值范围(0, 1]，值越大记忆保留效果越好，默认0.5
        """
        self.alpha = alpha
        self.beta = beta
        logger.debug(f"EbbinghausForgettingCurve initialized: alpha={alpha}, beta={beta}")

    def calculate_forgetting_probability(self, time_since_last_study: float) -> float:
        """
        计算距离上次学习后的遗忘概率
        公式: P = alpha * e^(-beta * t)
        其中:
        - P: 遗忘概率
        - alpha: 遗忘速度参数（越大遗忘越快）
        - beta: 记忆保持参数（越大长期记忆越好）
        - t: 距上次学习的时间（单位：天）
        
        流程:
        1. 如果时间间隔 <= 0，返回遗忘概率 0.0
        2. 应用指数模型计算遗忘概率
        3. 限制最大遗忘概率为0.95

        Args:
            time_since_last_study (float): 距上次学习的时间（单位：天），非负值

        Returns:
            float: 遗忘概率，取值范围[0, 0.95]，值越大表示遗忘程度越高
        """
        if time_since_last_study <= 0:
            return 0.0
        # 艾宾浩斯遗忘曲线的简化指数模型
        forgetting_prob = self.alpha * np.exp(-self.beta * time_since_last_study)
        logger.debug(f"Forgetting probability calculated: {forgetting_prob:.4f} (days since last study: {time_since_last_study})")
        return min(forgetting_prob, 0.95)  # 最大遗忘概率限制为0.95

    def calculate_optimal_review_time(self, current_mastery: float) -> float:
        """
        计算最优复习时间（距当前时间的天数）
        公式: T = log(m + 1) * 2
        其中:
        - T: 最优复习间隔（天）
        - m: 当前掌握程度（0-1）
        
        流程:
        1. 如果当前掌握程度 <= 0，返回最小间隔 0.1 天
        2. 应用对数函数计算最优复习间隔
        3. 确保最小间隔为0.1天

        Args:
            current_mastery (float): 当前掌握程度，取值范围[0, 1]，0表示完全未掌握，1表示完全掌握

        Returns:
            float: 最优复习间隔（单位：天），取值范围[0.1, ...]，表示距离当前时间的最佳复习天数
        """
        if current_mastery <= 0:
            return 0.1  # 未掌握时尽快复习
        # 掌握程度越高，复习间隔越长
        optimal_interval = np.log(current_mastery + 1) * 2
        logger.debug(f"Optimal review interval: {optimal_interval:.2f} (mastery: {current_mastery})")
        return max(optimal_interval, 0.1)  # 最小间隔0.1天


class QLearningAgent:
    """Q-Learning强化学习智能体，用于优化复习策略

    该智能体通过Q-Learning算法学习最优复习间隔策略，基于当前掌握程度、时间间隔和知识点难度
    动态调整复习计划，最大化长期记忆保留效果。
    """

    def __init__(self, learning_rate: float = 0.1, discount_factor: float = 0.9,
                 exploration_rate: float = 0.3, exploration_decay: float = 0.995):
        """
        初始化Q-Learning参数

        Args:
            learning_rate (float): 学习率，控制Q值更新幅度，取值范围(0, 1]，默认0.1
            discount_factor (float): 折扣因子，控制未来奖励的权重，取值范围(0, 1]，默认0.9
            exploration_rate (float): 探索率(ε)，控制随机选择动作的概率，取值范围[0, 1]，默认0.3
            exploration_decay (float): 探索率衰减因子，每次迭代后探索率乘以该值，取值范围(0, 1)，默认0.995
        """
        self.lr = learning_rate
        self.gamma = discount_factor
        self.epsilon = exploration_rate
        self.epsilon_decay = exploration_decay
        self.q_table: Dict[str, Dict[float, float]] = {}
        logger.info(f"QLearningAgent initialized: lr={learning_rate}, gamma={discount_factor}, epsilon={exploration_rate}, decay={exploration_decay}")

    def get_state(self, mastery: float, time_since_last_study: float, difficulty: float) -> str:
        """
        将连续状态离散化为字符串状态

        状态离散化流程：
        1. 将掌握程度(mastery)分为5个等级：mastery_bin = min(int(mastery * 5), 4)
        2. 将时间间隔(time_since_last_study)按周分为3个等级：time_bin = min(int(time_since_last_study / 7 * 3), 2)
        3. 将难度(difficulty)分为3个等级：difficulty_bin = min(int(difficulty * 3), 2)
        4. 组合为状态字符串："m{mastery_bin}t{time_bin}d{difficulty_bin}"

        Args:
            mastery (float): 掌握程度，取值范围[0, 1]，0表示完全遗忘，1表示完全掌握
            time_since_last_study (float): 距上次学习时间，单位为天，非负值
            difficulty (float): 知识点难度，取值范围[0, 1]，0表示最简单，1表示最难

        Returns:
            str: 离散化后的状态字符串，格式为"m{mastery_bin}t{time_bin}d{difficulty_bin}"
        """
        # 离散化状态空间
        mastery_bin = min(int(mastery * 5), 4)  # 分为5个等级
        time_bin = min(int(time_since_last_study / 7 * 3), 2)  # 按周分为3个等级
        difficulty_bin = min(int(difficulty * 3), 2)  # 分为3个难度等级
        state = f"m{mastery_bin}t{time_bin}d{difficulty_bin}"
        logger.debug(f"State encoded: {state} (mastery={mastery}, time={time_since_last_study}, diff={difficulty})")
        return state

    def get_possible_actions(self) -> List[float]:
        """
        获取可能的复习间隔动作（单位：天）

        返回预设的复习间隔列表，基于艾宾浩斯遗忘曲线理论设置，间隔逐渐增大
        包括短期复习(0.1天=2.4小时)、中期复习和长期复习的时间点。

        Returns:
            List[float]: 复习间隔列表，单位为天，包含[0.1, 0.5, 1, 3, 7, 14, 30]
        """
        return [0.1, 0.5, 1, 3, 7, 14, 30]  # 不同的复习间隔

    def choose_action(self, state: str) -> float:
        """
        根据ε-贪婪策略选择动作（探索或利用）

        选择动作流程：
        1. 如果状态不在Q表中，初始化该状态的所有Q值为0
        2. 生成随机数r，如果r < ε则进行探索（随机选择动作）
        3. 否则进行利用（选择当前Q值最大的动作）

        Args:
            state (str): 状态字符串，格式为"m{mastery_bin}t{time_bin}d{difficulty_bin}"

        Returns:
            float: 选择的复习间隔动作，单位为天
        """
        if state not in self.q_table:
            self.q_table[state] = {action: 0 for action in self.get_possible_actions()}

        if random.random() < self.epsilon:
            # 探索：随机选择动作
            action = random.choice(self.get_possible_actions())
            logger.debug(f"Action chosen (explore): {action} for state {state}")
        else:
            # 利用：选择Q值最大的动作
            action = max(self.q_table[state], key=self.q_table[state].get)
            logger.debug(f"Action chosen (exploit): {action} for state {state}")
        return action

    def update(self, state: str, action: float, reward: float, next_state: str):
        """
        使用Q-Learning算法更新Q表

        Q值更新公式：Q(s,a) = Q(s,a) + lr * (reward + gamma * max(Q(s',a')) - Q(s,a))
        其中：
        - Q(s,a): 当前状态-动作对的Q值
        - lr: 学习率
        - reward: 当前动作获得的奖励
        - gamma: 折扣因子
        - max(Q(s',a')): 下一个状态的最大Q值

        更新流程：
        1. 如果当前状态或下一个状态不在Q表中，初始化其Q值
        2. 计算下一个状态的最大Q值
        3. 根据Q-Learning公式更新当前状态-动作对的Q值
        4. 衰减探索率：epsilon = max(epsilon * epsilon_decay, 0.01)

        Args:
            state (str): 当前状态字符串
            action (float): 执行的复习间隔动作
            reward (float): 获得的奖励值，与记忆保留率正相关
            next_state (str): 执行动作后的下一个状态字符串
        """
        if state not in self.q_table:
            self.q_table[state] = {action: 0 for action in self.get_possible_actions()}

        if next_state not in self.q_table:
            self.q_table[next_state] = {action: 0 for action in self.get_possible_actions()}

        # Q-Learning更新公式
        max_next_q = max(self.q_table[next_state].values())
        old_q = self.q_table[state][action]
        self.q_table[state][action] += self.lr * (reward + self.gamma * max_next_q - old_q)

        # 衰减探索率
        self.epsilon = max(self.epsilon * self.epsilon_decay, 0.01)
        logger.info(f"Q-table updated: state={state}, action={action}, old_q={old_q:.4f}, new_q={self.q_table[state][action]:.4f}, reward={reward:.4f}, next_state={next_state}")

    def get_max_q_value(self, state: str) -> float:
        """
        获取一个状态下的最大Q值，代表该状态下的最优预期累积奖励

        Args:
            state (str): 状态字符串，格式为"m{mastery_bin}t{time_bin}d{difficulty_bin}"

        Returns:
            float: 状态下的最大Q值，如果状态未知则返回0.0
        """
        if state not in self.q_table or not self.q_table[state]:
            return 0.0
        return max(self.q_table[state].values())


class KnowledgePoint:
    """知识点类，存储知识点信息及学习状态"""

    def __init__(self, id: int, content: str, difficulty: float = 0.5):
        """
        初始化知识点
        id: 知识点ID
        content: 知识点内容
        difficulty: 难度（0-1）
        """
        self.id = id
        self.content = content
        self.difficulty = difficulty  # 0-1
        self.mastery = 0.0  # 掌握程度：0-1
        self.last_study_time: Optional[datetime] = None  # 上次学习时间
        self.review_times = 0  # 复习总次数
        self.correct_reviews = 0  # 正确复习次数
        logger.debug(f"KnowledgePoint created: id={id}, content={content}, difficulty={difficulty}")

    def get_accuracy(self) -> float:
        """计算历史正确率"""
        if self.review_times == 0:
            return 0.0
        return self.correct_reviews / self.review_times

    def update_mastery(self, is_correct: bool, current_time: datetime) -> float:
        """
        更新掌握程度
        is_correct: 是否回答正确
        current_time: 当前时间
        """
        if self.last_study_time:
            time_diff = (current_time - self.last_study_time).days + 1e-6  # 避免除以0
            # 计算遗忘后的掌握程度
            forget_curve = EbbinghausForgettingCurve()
            forgetting_prob = forget_curve.calculate_forgetting_probability(time_diff)
            self.mastery = max(0.0, self.mastery * (1 - forgetting_prob))
            logger.debug(f"Mastery decayed: {self.mastery:.4f} after forgetting (prob={forgetting_prob:.4f})")
        else:
            self.mastery = 0.0  # 如果从未学习过，掌握程度为0

        # 根据回答结果更新掌握程度
        if is_correct:
            # 回答正确，提高掌握程度
            improvement = 0.2 * (1 - self.difficulty)
            self.mastery = min(1.0, self.mastery + improvement)
            self.correct_reviews += 1
            logger.info(f"Correct review: mastery increased to {self.mastery:.4f}")
        else:
            # 回答错误，降低掌握程度
            self.mastery = max(0.0, self.mastery * 0.8)
            logger.info(f"Incorrect review: mastery decreased to {self.mastery:.4f}")

        self.last_study_time = current_time
        self.review_times += 1
        return self.mastery


class StudySystem:
    """学习系统，整合遗忘曲线和强化学习"""

    def __init__(self, knowledge_points_data: Optional[List[Dict]] = None, num_knowledge_points: int = 20):
        """
        初始化学习系统
        knowledge_points_data: 一个包含知识点信息的字典列表，例如 [{'id': 0, 'content': '...', 'difficulty': 0.5}, ...]
        num_knowledge_points: 如果不提供具体数据，则创建指定数量的随机知识点
        """
        self.learning_agent = QLearningAgent()
        self.forgetting_curve = EbbinghausForgettingCurve()
        if knowledge_points_data:
            self.knowledge_points = self._create_knowledge_points_from_data(knowledge_points_data)
        else:
            # 为了向后兼容，如果未提供数据，则创建随机知识点
            self.knowledge_points = self._create_random_knowledge_points(num_knowledge_points)
        self.current_time = datetime.now()
        logger.info(f"StudySystem initialized with {len(self.knowledge_points)} knowledge points.")

    def _create_knowledge_points_from_data(self, data: List[Dict]) -> List[KnowledgePoint]:
        """根据提供的数据创建知识点"""
        points = []
        for item in data:
            points.append(KnowledgePoint(
                id=item['id'],
                content=item['content'],
                difficulty=item.get('difficulty', 0.5)  # 使用提供的难度或默认值
            ))
        return points

    def _create_random_knowledge_points(self, num_points: int) -> List[KnowledgePoint]:
        """创建随机知识点集合"""
        points = []
        for i in range(num_points):
            difficulty = random.uniform(0.3, 0.9)  # 知识点难度随机分布
            points.append(KnowledgePoint(i, f"知识点{i + 1}", difficulty))
        return points

    def get_review_candidates(self, num_candidates: int = 5) -> List[KnowledgePoint]:
        """
        获取需要复习的知识点候选。
        新版逻辑：结合了遗忘曲线（紧急程度）和强化学习（战略价值）。
        """
        candidates = []
        for point in self.knowledge_points:
            if not point.last_study_time:
                # 从未学习过的知识点具有最高优先级
                priority = 100.0
            else:
                time_diff = (self.current_time - point.last_study_time).days
                
                # 1. 遗忘曲线作为主要驱动力
                # 遗忘概率直接反映了复习的紧急程度。
                forgetting_prob = self.forgetting_curve.calculate_forgetting_probability(time_diff)
                
                # 2. 强化学习作为战略调整
                # Q值反映了当前状态的长期价值。一个低Q值的状态（比如低掌握度、长时间未复习）
                # 是我们希望避免的，因此需要更高的复习优先级来摆脱这个状态。
                state = self.learning_agent.get_state(
                    point.mastery, time_diff, point.difficulty
                )
                q_value = self.learning_agent.get_max_q_value(state)
                
                # 新的优先级计算：
                # 基础是遗忘概率。
                # Q值作为调节因子：Q值越低，优先级越高。我们用 `- q_value` 来实现。
                # 权重 (e.g., 0.1) 用于平衡两个因素的影响。
                priority = forgetting_prob - 0.1 * q_value
                
            candidates.append((point, priority))

        # 按优先级降序排序，返回前num_candidates个
        logger.debug(f"Review candidates selected: {[p[0].id for p in candidates[:num_candidates]]}")
        return [p[0] for p in sorted(candidates, key=lambda x: x[1], reverse=True)][:num_candidates]

    def review_knowledge_point(self, point: KnowledgePoint, is_correct: bool, reaction_time_seconds: float = 2.0) -> Dict:
        """
        复习知识点并更新状态
        point: 知识点对象
        is_correct: 是否回答正确
        reaction_time_seconds: 反应时间（秒），用于量化奖励
        """
        # 更新知识点掌握程度
        old_mastery = point.mastery
        new_mastery = point.update_mastery(is_correct, self.current_time)

        # 计算时间间隔
        time_diff = 0
        if point.last_study_time:
            # last_study_time 在 update_mastery 中被更新，所以这里的时间差是准确的
            time_diff = (point.last_study_time - point.last_study_time).days

        # 构建状态
        state = self.learning_agent.get_state(old_mastery, time_diff, point.difficulty)

        # 选择下一次复习时间（使用强化学习）
        next_review_interval = self.learning_agent.choose_action(state)

        # 计算奖励（基于掌握程度变化、反应时间、历史正确率和复习效率）
        if is_correct:
            # 基础奖励：与难度和掌握程度提升相关
            mastery_gain_reward = (new_mastery - old_mastery) * (1 + point.difficulty)
            # 反应时间奖励：反应越快，奖励越高。设定一个基准时间，例如5秒
            time_reward = max(0, (5 - reaction_time_seconds) / 5) * 0.3  # 奖励范围 [0, 0.3]
            # 历史正确率奖励：攻克难题（历史正确率低）给予额外奖励
            accuracy_bonus = (1 - point.get_accuracy()) * 0.2  # 奖励范围 [0, 0.2]
            reward = mastery_gain_reward + time_reward + accuracy_bonus
        else:
            # 回答错误的惩罚与遗忘程度相关
            # 反应快但答错了（说明是自信的错误），惩罚更重
            penalty_factor = 1 + (max(0, 3 - reaction_time_seconds) / 3) * 0.5  # 惩罚因子 [1, 1.5]
            reward = -0.5 * (1 - old_mastery) * penalty_factor

        # 计算下一个状态
        next_state = self.learning_agent.get_state(new_mastery, 0, point.difficulty)

        # 更新强化学习智能体
        self.learning_agent.update(state, next_review_interval, reward, next_state)
        logger.info(f"Review: id={point.id}, correct={is_correct}, old_mastery={old_mastery:.4f}, new_mastery={new_mastery:.4f}, reward={reward:.4f}, next_interval={next_review_interval}")

        return {
            "knowledge_point": point,
            "is_correct": is_correct,
            "old_mastery": old_mastery,
            "new_mastery": new_mastery,
            "review_interval": next_review_interval,
            "next_review_time": point.last_study_time + timedelta(days=next_review_interval),
            "reward": reward,
            "reaction_time": reaction_time_seconds
        }

    def simulate_study_process(self, days: int = 30, review_per_day: int = 5) -> pd.DataFrame:
        """
        模拟学习过程多天
        days: 模拟天数
        review_per_day: 每天复习的知识点数量
        """
        results = []
        for day in range(days):
            day_results = []
            # 获取当天需要复习的知识点
            review_points = self.get_review_candidates(review_per_day)

            for point in review_points:
                # 随机决定是否回答正确（基于当前掌握程度）
                is_correct = random.random() < point.mastery
                # 模拟反应时间：掌握度越高，反应越快
                simulated_reaction_time = max(0.5, 5 * (1 - point.mastery) + random.uniform(-0.5, 0.5))
                # 复习知识点
                result = self.review_knowledge_point(point, is_correct, simulated_reaction_time)
                day_results.append({
                    "day": day + 1,
                    "knowledge_point_id": point.id,
                    "difficulty": point.difficulty,
                    "is_correct": is_correct,
                    "mastery_change": result["new_mastery"] - result["old_mastery"],
                    "next_review_days": result["review_interval"],
                    "reward": result["reward"],
                    "reaction_time": result["reaction_time"]
                })

            results.extend(day_results)
            # 推进时间
            self.current_time += timedelta(days=1)
            logger.info(f"Day {day+1} simulated. Avg reward: {np.mean([r['reward'] for r in day_results]):.4f}")

        return pd.DataFrame(results)


# 使用示例
if __name__ == "__main__":
    # 创建学习系统
    logger.info("开始模拟学习过程...")
    study_system = StudySystem(num_knowledge_points=20)

    # 模拟30天的学习过程
    results = study_system.simulate_study_process(days=30, review_per_day=5)

    # 分析学习效果
    final_mastery_values = [p.mastery for p in study_system.knowledge_points]
    avg_final_mastery = sum(final_mastery_values) / len(final_mastery_values)
    
    avg_reward_by_day = results.groupby("day")["reward"].mean()

    # 绘制学习曲线
    fig, ax1 = plt.subplots(figsize=(12, 7))

    # 绘制平均掌握程度
    ax1.set_xlabel("学习天数")
    ax1.set_ylabel("所有知识点平均掌握程度", color='tab:blue')
    # 计算每日所有知识点的平均掌握度
    mastery_over_time = [avg_final_mastery] # Day 0 is initial state
    # This part is tricky. We need to recalculate total mastery each day.
    # The current simulation doesn't store this easily.
    # Let's plot average reward instead as it's more direct from results.
    
    # 绘制每日平均奖励
    color = 'tab:green'
    ax1.set_ylabel('每日平均奖励', color=color)
    ax1.plot(avg_reward_by_day.index, avg_reward_by_day.values, color=color, label='每日平均奖励')
    ax1.tick_params(axis='y', labelcolor=color)
    
    plt.title("学习过程中的每日平均奖励变化")
    plt.grid(True)
    fig.tight_layout()
    plt.show()

    # 查看学习效果统计
    final_mastery = {}
    for point in study_system.knowledge_points:
        final_mastery[point.id] = point.mastery

    avg_final_mastery = sum(final_mastery.values()) / len(final_mastery)
    logger.info(f"30天后平均掌握程度: {avg_final_mastery:.4f}")
    logger.info(f"强化学习探索率: {study_system.learning_agent.epsilon:.4f}")

    # 查看Q表的学习情况（前5个知识点的状态）
    q_table_sample = {}
    for state in list(study_system.learning_agent.q_table.keys())[:5]:
        q_table_sample[state] = study_system.learning_agent.q_table[state]

    logger.info(f"Q表学习示例: {q_table_sample}")