import os
import sys
from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from python.service.QLearning import EbbinghausForgettingCurve, QLearningAgent
from python.service.models import User, KnowledgePoint, UserKnowledge, SessionLocal
import json

class DBKnowledgePoint:
    """数据库知识点对象，兼容 QLearning.py 的 KnowledgePoint 接口，支持多题型"""
    def __init__(self, kp: KnowledgePoint, uk: UserKnowledge = None):
        self.id = kp.id
        self.content = kp.content
        self.difficulty = kp.difficulty
        self.type = getattr(kp, 'type', 'single')
        raw_options = getattr(kp, 'options', None)
        if raw_options is None or raw_options.strip() == "":
            self.options = None
        else:
            try:
                self.options = json.loads(raw_options)
            except Exception:
                self.options = raw_options
        self.answer = kp.answer if hasattr(kp, 'answer') else None
        self.mastery = uk.mastery if uk else 0.0
        self.last_study_time = uk.last_study_time if uk else None
        self.review_times = uk.review_times if uk else 0
        self.correct_reviews = uk.correct_reviews if uk else 0
        self.last_answer = uk.last_answer if uk else None
        self.is_last_correct = uk.is_last_correct if uk else None
        self._uk = uk

    def get_accuracy(self):
        if self.review_times == 0:
            return 0.0
        return self.correct_reviews / self.review_times

    def update_mastery(self, is_correct: bool, current_time: datetime):
        if self.last_study_time:
            time_diff = (current_time - self.last_study_time).days + 1e-6
            forget_curve = EbbinghausForgettingCurve()
            forgetting_prob = forget_curve.calculate_forgetting_probability(time_diff)
            self.mastery = max(0.0, self.mastery * (1 - forgetting_prob))
        if is_correct:
            improvement = 0.2 * (1 - self.difficulty)
            self.mastery = min(1.0, self.mastery + improvement)
            self.correct_reviews += 1
        else:
            self.mastery = max(0.0, self.mastery * 0.8)
        self.last_study_time = current_time
        self.review_times += 1
        return self.mastery

    def save_to_db(self, db, user_id, user_answer=None, is_last_correct=None):
        if not self._uk:
            self._uk = UserKnowledge(user_id=user_id, knowledge_point_id=self.id)
            db.add(self._uk)
        self._uk.mastery = self.mastery
        self._uk.last_study_time = self.last_study_time
        self._uk.review_times = self.review_times
        self._uk.correct_reviews = self.correct_reviews
        if user_answer is not None:
            self._uk.last_answer = json.dumps(user_answer, ensure_ascii=False) if not isinstance(user_answer, str) else user_answer
        if is_last_correct is not None:
            self._uk.is_last_correct = is_last_correct
        db.commit()

class DBStudySystem:
    """基于数据库的学习系统，兼容 QLearning.py 的 StudySystem 接口，支持多题型"""
    def __init__(self, user_id, db):
        self.user_id = user_id
        self.db = db
        self.learning_agent = QLearningAgent()
        self.forgetting_curve = EbbinghausForgettingCurve()
        self.current_time = datetime.now()
        self.knowledge_points = self._load_knowledge_points(self.db)

    def _load_knowledge_points(self, db):
        kps = db.query(KnowledgePoint).all()
        user_kps = {uk.knowledge_point_id: uk for uk in db.query(UserKnowledge).filter_by(user_id=self.user_id).all()}
        return [DBKnowledgePoint(kp, user_kps.get(kp.id)) for kp in kps]

    def get_review_candidates(self, num_candidates=5):
        candidates = []
        for point in self.knowledge_points:
            if not point.last_study_time:
                priority = 100.0
            else:
                time_diff = (self.current_time - point.last_study_time).days
                forgetting_prob = self.forgetting_curve.calculate_forgetting_probability(time_diff)
                state = self.learning_agent.get_state(point.mastery, time_diff, point.difficulty)
                q_value = self.learning_agent.get_max_q_value(state)
                priority = forgetting_prob - 0.1 * q_value
            candidates.append((point, priority))
        return [p[0] for p in sorted(candidates, key=lambda x: x[1], reverse=True)][:num_candidates]

    def review_knowledge_point(self, point: DBKnowledgePoint, user_answer, reaction_time_seconds: float = 2.0, current_time=None):
        """
        复习知识点并自动判分，支持多题型。
        user_answer: 用户提交的答案（字符串、list、dict等）
        reaction_time_seconds: 用户作答用时
        """
        is_correct = self._judge_answer(point, user_answer)
        old_mastery = point.mastery
        now = current_time or self.current_time
        new_mastery = point.update_mastery(is_correct, now)
        time_diff = 0
        if point.last_study_time:
            time_diff = (point.last_study_time - point.last_study_time).days
        state = self.learning_agent.get_state(old_mastery, time_diff, point.difficulty)
        next_review_interval = self.learning_agent.choose_action(state)
        if is_correct:
            mastery_gain_reward = (new_mastery - old_mastery) * (1 + point.difficulty)
            time_reward = max(0, (5 - reaction_time_seconds) / 5) * 0.3
            accuracy_bonus = (1 - point.get_accuracy()) * 0.2
            reward = mastery_gain_reward + time_reward + accuracy_bonus
        else:
            penalty_factor = 1 + (max(0, 3 - reaction_time_seconds) / 3) * 0.5
            reward = -0.5 * (1 - old_mastery) * penalty_factor
        next_state = self.learning_agent.get_state(new_mastery, 0, point.difficulty)
        self.learning_agent.update(state, next_review_interval, reward, next_state)
        point.save_to_db(SessionLocal(), self.user_id, user_answer, is_correct)
        return {
            "knowledge_point": point,
            "is_correct": is_correct,
            "old_mastery": old_mastery,
            "new_mastery": new_mastery,
            "review_interval": next_review_interval,
            "next_review_time": point.last_study_time,
            "reward": reward,
            "reaction_time": reaction_time_seconds,
            "user_answer": user_answer
        }

    def _judge_answer(self, point, user_answer):
        """
        通用判分逻辑，支持判断题、单选题、多选题、填空题、简答题等。
        """
        try:
            if point.type == 'judge':
                # 判断题，答案通常为 'true'/'false' 或 '1'/'0'
                return str(user_answer).strip().lower() == str(point.answer).strip().lower()
            elif point.type == 'single':
                # 单选题，答案为字符串或选项key
                return str(user_answer).strip() == str(point.answer).strip()
            elif point.type == 'multi':
                # 多选题，答案和用户答案都转为集合
                if isinstance(user_answer, str):
                    user_set = set(json.loads(user_answer))
                else:
                    user_set = set(user_answer)
                std_set = set(json.loads(point.answer)) if isinstance(point.answer, str) else set(point.answer)
                return user_set == std_set
            elif point.type == 'fill':
                # 填空题，答案和用户答案都转为小写去空格
                return str(user_answer).strip().lower() == str(point.answer).strip().lower()
            elif point.type == 'short':
                # 简答题，简单包含关系（可扩展为人工批改）
                return str(point.answer).strip().lower() in str(user_answer).strip().lower()
        except Exception as e:
            # 判分异常一律判错
            return False
        return False

if __name__ == "__main__":
    # 示例：命令行交互
    if len(sys.argv) < 2:
        print("用法: python db_study_system.py <user_id>")
        sys.exit(1)
    user_id = int(sys.argv[1])
    system = DBStudySystem(user_id)
    print(f"用户 {user_id} 的知识点复习候选:")
    for kp in system.get_review_candidates(5):
        print(f"ID={kp.id} 内容={kp.content} 掌握度={kp.mastery:.2f}")
    # 可扩展更多命令行交互功能 