<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>知识点提交</title>
    <style>
        body { font-family: 'Segoe UI', system-ui, -apple-system, sans-serif; background: #f7fafc; line-height: 1.5; }
        .container { max-width: 600px; margin: 20px auto; background: #fff; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.05); padding: 32px; }
        h2 { text-align: center; color: #1a202c; margin-bottom: 24px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 8px; color: #4a5568; font-weight: 500; }
        label.required::after { content: '*'; color: #e53e3e; margin-left: 4px; }
        input, select, textarea { width: 100%; padding: 10px 12px; margin-top: 6px; border-radius: 6px; border: 1px solid #cbd5e0; font-size: 1rem; transition: border-color 0.2s; }
        input:focus, select:focus, textarea:focus { outline: none; border-color: #5e72e4; box-shadow: 0 0 0 3px rgba(94, 114, 228, 0.1); }
        .options-area, .answer-area { margin-top: 12px; padding: 16px; background: #f9fafb; border-radius: 6px; border: 1px solid #edf2f7; transition: all 0.3s ease; }
        .msg { min-height: 32px; line-height: 32px; color: #e53e3e; text-align: center; margin: 16px 0; padding: 8px; border-radius: 6px; background: #fff5f5; border: 1px solid #fed7d7; display: none; }
        .msg.show { display: block; }
        .msg.success { color: #22c55e; background: #f0fdf4; border-color: #bbf7d0; }
        button { margin-top: 18px; width: 100%; padding: 12px; background: #5e72e4; color: #fff; border: none; border-radius: 6px; font-size: 1.1rem; font-weight: 600; cursor: pointer; transition: background 0.2s; }
        button:hover { background: #4a5fc1; }
        button.secondary { background: #22c55e; margin-top: 10px; }
        button.secondary:hover { background: #16a34a; }
        .difficulty-control { display: flex; align-items: center; gap: 12px; margin-top: 6px; }
        .difficulty-control input { flex: 1; }
        .difficulty-value { min-width: 40px; text-align: center; color: #4a5568; }
    </style>
</head>
<body>
    <div class="container">
        <h2>知识点提交</h2>
        <form id="knowledgeForm" onsubmit="return submitForm(event)">
        <div class="form-group">
            <label class="required">知识点内容
                <textarea id="content" rows="3" required></textarea>
            </label>
        </div>
        <div class="form-group">
            <label class="required">难度 (0-1)
                <div class="difficulty-control">
                    <input type="range" id="difficulty" min="0" max="1" step="0.1" value="0.5" required>
                    <span class="difficulty-value">0.5</span>
                </div>
            </label>
        </div>
        <div class="form-group">
            <label class="required">题型
                <select id="type" onchange="onTypeChange()">
                    <option value="single">单选题</option>
                    <option value="multi">多选题</option>
                    <option value="judge">判断题</option>
                    <option value="fill">填空题</option>
                    <option value="short">简答题</option>
                    <option value="recite">默写/背诵题</option>
                </select>
            </label>
        </div>
        <div class="options-area" id="options-area" style="display:none;">
            <label>选项（每行一个）:</label>
            <textarea id="options" name="options" rows="4"></textarea>
        </div>
        <div class="answer-area" id="answer-area">
            <label id="answer-label">标准答案
                <input type="text" id="answer" required>
            </label>
        </div>
        <button type="submit">提交</button>
        <button type="button" onclick="window.location.href='/static/study.html'" class="secondary">去复习知识点</button>
        <div class="msg" id="msg"></div>
        </form>
        <div id="knowledge-list" style="margin-top:32px;"></div>
    </div>
    <script>
        const typeSelect = document.getElementById('type');
        const optionsArea = document.getElementById('options-area');
        const answerArea = document.getElementById('answer-area');

        const optionsInput = document.getElementById('options');
        const msg = document.getElementById('msg');

        function renderAnswerInput() {
            const type = typeSelect.value;
            answerArea.innerHTML = '';
            answerArea.style.display = 'block';
            if (type === 'single' || type === 'multi') {
                optionsArea.style.display = 'block';
                let opts = optionsInput.value.trim().split('\n').filter(x=>x.trim());
                if (opts.length === 0) {
                    answerArea.innerHTML = '<div style="color:#e53e3e;padding:8px;">请先填写选项</div>';
                    return;
                }
                if (type === 'single') {
                    let html = '<label id="answer-label">标准答案</label><div class="radio-group">';
                    opts.forEach((opt, index) => {
                        html += `<div><input type="radio" id="answer-${index}" name="answer" value="${opt}">`;
                        html += `<label for="answer-${index}">${opt}</label></div>`;
                    });
                    html += '</div>';
                    answerArea.innerHTML = html;
                } else {
                    let html = '<label id="answer-label">标准答案（可多选）</label><div class="checkbox-group">';
                    opts.forEach((opt, index) => {
                        html += `<div><input type="checkbox" id="answer-${index}" name="answer" value="${opt}">`;
                        html += `<label for="answer-${index}">${opt}</label></div>`;
                    });
                    html += '</div>';
                    answerArea.innerHTML = html;
                }
            } else if (type === 'judge') {
                optionsArea.style.display = 'none';
                answerArea.innerHTML = `<label id="answer-label">标准答案
                    <select id="answer">
                        <option value="true">正确</option>
                        <option value="false">错误</option>
                    </select>
                </label>`;
            } else if (type === 'recite') {
                optionsArea.style.display = 'none';
                answerArea.innerHTML = '';
            } else if (type === 'short') {
                optionsArea.style.display = 'none';
                answerArea.innerHTML = `<label id="answer-label">标准答案
                    <textarea id="answer" rows="3" required style="width:100%;padding:8px;"></textarea>
                </label>`;
            } else {
                optionsArea.style.display = 'none';
                answerArea.innerHTML = `<label id="answer-label">标准答案
                    <input type="text" id="answer" required>
                </label>`;
            }
        }

        window.onload = function() {
            renderAnswerInput();
            loadKnowledgeList();
            // 添加选项输入监听
            optionsInput.addEventListener('input', renderAnswerInput);
            // 初始化难度值显示
            document.querySelector('.difficulty-value').textContent = document.getElementById('difficulty').value;
            // 添加难度滑块事件监听
            document.getElementById('difficulty').addEventListener('input', function() {
                document.querySelector('.difficulty-value').textContent = this.value;
            });
        };

        function onTypeChange() {
            var optionsDiv = document.getElementById('options-area');
            var type = document.getElementById('type').value;
            if (type === 'single' || type === 'multi') {
                optionsDiv.style.display = 'block';
                document.getElementById('options').required = true;
            } else {
                optionsDiv.style.display = 'none';
                document.getElementById('options').required = false;
                document.getElementById('options').value = '';
            }
            renderAnswerInput(); // 添加此行以重新渲染答案输入区域
        }

        function submitForm(event) {
            event.preventDefault();
            msg.textContent = '';
            const type = typeSelect.value;
            let options = null;
            let answer = undefined;
            if (type === 'single' || type === 'multi') {
                options = optionsInput.value.trim().split('\n').filter(x=>x.trim());
                if (options.length < 2) {
                    msg.textContent = '选择题至少需要两个选项';
                    return false;
                }
                if (type === 'single') {
                    const selectedRadio = document.querySelector('input[name="answer"]:checked');
                    if (!selectedRadio) {
                        msg.textContent = '请选择标准答案';
                        return false;
                    }
                    answer = selectedRadio.value;
                    if (!options.includes(answer)) {
                        msg.textContent = '标准答案必须在选项中';
                        return false;
                    }
                } else {
                    // 多选题
                    const selectedCheckboxes = Array.from(document.querySelectorAll('input[name="answer"]:checked'));
                    if (selectedCheckboxes.length === 0) {
                        msg.textContent = '请至少选择一个标准答案';
                        return false;
                    }
                    answer = selectedCheckboxes.map(cb => cb.value).join(',');
                }
                options = options.join(',');
            } else if (type === 'judge') {
                answer = document.getElementById('answer').value;
                options = JSON.stringify(['正确', '错误']);
            } else if (type === 'recite') {
                // 默写/背诵题不需要答案
                answer = undefined;
                options = null;
            } else {
                // 填空、简答
                answer = document.getElementById('answer').value.trim();
                if (!answer) {
                    msg.textContent = '请填写标准答案';
                    return false;
                }
                options = null;
            }

            const data = {
                content: document.getElementById('content').value.trim(),
                difficulty: parseFloat(document.getElementById('difficulty').value),
                type: type,
                options: options
            };
            if (answer !== undefined) data.answer = answer;

            fetch('/knowledge/submit', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(data)
            }).then(async res => {
                if (res.ok) {
                    msg.textContent = '✅ 提交成功！知识点已添加';
                    msg.className = 'msg success show';
                    document.getElementById('knowledgeForm').reset();
                    renderAnswerInput();
                    // 滚动到知识点列表
                    setTimeout(() => {
                        document.getElementById('knowledge-list').scrollIntoView({behavior: 'smooth'});
                        // 刷新知识点列表
                        loadKnowledgeList();
                    }, 1000);
                } else {
                    const err = await res.json();
                    msg.textContent = '❌ ' + (err.detail || '提交失败，请重试');
                    msg.className = 'msg show';
                }
            }).catch(() => {
                msg.textContent = '❌ 网络错误，提交失败';
                msg.className = 'msg show';
            });
            return false;
        }

        function loadKnowledgeList() {
            fetch('/knowledge/all')
                .then(res => res.json())
                .then(data => {
                    const listDiv = document.getElementById('knowledge-list');
                    if (!data || data.length === 0) {
                        listDiv.innerHTML = '<div style="color:#888;text-align:center;">暂无知识点</div>';
                        return;
                    }
                    let html = '<h3 style="margin-bottom:12px;">已上传知识点</h3>';
                    html += '<table style="width:100%;border-collapse:collapse;font-size:0.98rem;">';
                    html += '<tr style="background:#f7fafc;"><th style="padding:6px 4px;">题干</th><th style="padding:6px 4px;">题型</th><th style="padding:6px 4px;">难度</th></tr>';
                    data.forEach(kp => {
                        html += `<tr><td style='padding:6px 4px;border-bottom:1px solid #eee;'>${kp.content}</td><td style='padding:6px 4px;border-bottom:1px solid #eee;'>${typeLabel(kp.type)}</td><td style='padding:6px 4px;border-bottom:1px solid #eee;'>${kp.difficulty}</td></tr>`;
                    });
                    html += '</table>';
                    listDiv.innerHTML = html;
                });
        }

        function typeLabel(type) {
            switch(type) {
                case 'single': return '单选题';
                case 'multi': return '多选题';
                case 'judge': return '判断题';
                case 'fill': return '填空题';
                case 'short': return '简答题';
                case 'recite': return '默写/背诵题';
                default: return type;
            }
        }
    </script>
</body>
</html>