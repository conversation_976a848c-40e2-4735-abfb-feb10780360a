from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from python.service.models import SessionLocal
from python.service.db_study_system import DBStudySystem
from pydantic import BaseModel
from typing import List, Any

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class ReviewRequest(BaseModel):
    user_id: int
    knowledge_point_id: int
    user_answer: Any
    reaction_time_seconds: float = 2.0

@router.get("/candidates")
def get_candidates(user_id: int, db: Session = Depends(get_db)):
    system = DBStudySystem(user_id, db)
    candidates = system.get_review_candidates()
    return [
        {
            "id": kp.id,
            "content": kp.content,
            "type": kp.type,
            "options": kp.options,
            "mastery": kp.mastery
        } for kp in candidates
    ]

@router.post("/review")
def review_knowledge(req: ReviewRequest, db: Session = Depends(get_db)):
    system = DBStudySystem(req.user_id, db)
    kp = next((k for k in system.knowledge_points if k.id == req.knowledge_point_id), None)
    if not kp:
        raise HTTPException(status_code=404, detail="知识点不存在")
    result = system.review_knowledge_point(kp, req.user_answer, req.reaction_time_seconds)
    return {
        "is_correct": result["is_correct"],
        "old_mastery": result["old_mastery"],
        "new_mastery": result["new_mastery"],
        "review_interval": result["review_interval"],
        "next_review_time": result["next_review_time"],
        "reward": result["reward"]
    }

@router.get("/all_knowledge")
def get_all_knowledge(user_id: int, db: Session = Depends(get_db)):
    system = DBStudySystem(user_id, db)
    return [
        {
            "id": kp.id,
            "content": kp.content,
            "type": kp.type,
            "options": kp.options,
            "mastery": kp.mastery,
            "difficulty": kp.difficulty,
            "last_study_time": kp.last_study_time.isoformat() if kp.last_study_time else None,
            "review_times": kp.review_times,
            "correct_reviews": kp.correct_reviews
        } for kp in system.knowledge_points
    ]