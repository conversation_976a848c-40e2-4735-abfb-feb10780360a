from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, UniqueConstraint, ForeignKey, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

DATABASE_URL = "sqlite:///./instance/cramming.db"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

class User(Base):
    __tablename__ = "user"
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(80), unique=True, nullable=False)
    password_hash = Column(String(128), nullable=False)
    def set_password(self, password):
        from werkzeug.security import generate_password_hash
        self.password_hash = generate_password_hash(password)
    def check_password(self, password):
        from werkzeug.security import check_password_hash
        return check_password_hash(self.password_hash, password)

class KnowledgePoint(Base):
    __tablename__ = "knowledge_point"
    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text, nullable=False)
    difficulty = Column(Float, nullable=False)
    type = Column(String(20), default='single', nullable=False)  # 题型
    options = Column(Text, nullable=True)  # JSON字符串，选择题用
    answer = Column(Text, nullable=True)  # 标准答案，JSON或字符串，记忆题可为空

class UserKnowledge(Base):
    __tablename__ = "user_knowledge"
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    knowledge_point_id = Column(Integer, ForeignKey('knowledge_point.id'), nullable=False)
    mastery = Column(Float, default=0.0)
    last_study_time = Column(DateTime, nullable=True)
    review_times = Column(Integer, default=0)
    correct_reviews = Column(Integer, default=0)
    last_answer = Column(Text, nullable=True)  # 用户最近一次作答
    is_last_correct = Column(Boolean, nullable=True)
    __table_args__ = (UniqueConstraint('user_id', 'knowledge_point_id', name='_user_knowledge_uc'),) 