# 强化学习结合遗忘曲线的系统流程图与公式

## 流程图

```
[初始化系统] --> [创建知识点]
[创建知识点] --> [初始化Q-Learning代理]
[初始化Q-Learning代理] --> [模拟学习过程]
[模拟学习过程] --> [获取复习候选知识点]
[获取复习候选知识点] --> [复习知识点]
[复习知识点] --> [更新知识点掌握程度]
[更新知识点掌握程度] --> [计算奖励]
[计算奖励] --> [更新Q表]
[更新Q表] --> [推进时间]
[推进时间] --> [继续模拟]
```

## 公式

### 艾宾浩斯遗忘曲线公式
$$ P(t) = \alpha e^{-\beta t} $$
- $ P(t) $: 遗忘概率
- $ \alpha $: 遗忘速度参数
- $ \beta $: 记忆保持参数
- $ t $: 距上次学习的时间（天）

### Q-Learning更新公式
$$ Q(s, a) = Q(s, a) + \alpha [r + \gamma \max_{a'} Q(s', a') - Q(s, a)] $$
- $ Q(s, a) $: 状态-动作值函数
- $ \alpha $: 学习率
- $ r $: 奖励
- $ \gamma $: 折扣因子
- $ s' $: 下一个状态
- $ a' $: 下一个动作

### 最优复习时间计算公式
$$ T = \log(M + 1) \times 2 $$
- $ T $: 最优复习时间（天）
- $ M $: 当前掌握程度

### 掌握程度更新公式
$$ M_{new} = M_{old} \times (1 - P) + \Delta M $$
- $ M_{new} $: 新的掌握程度
- $ M_{old} $: 旧的掌握程度
- $ P $: 遗忘概率
- $ \Delta M $: 掌握程度变化

### 奖励计算公式
$$ R = \Delta M \times (1 + D) + T_R + A_B $$
- $ R $: 奖励
- $ \Delta M $: 掌握程度变化
- $ D $: 知识点难度
- $ T_R $: 反应时间奖励
- $ A_B $: 历史正确率奖励