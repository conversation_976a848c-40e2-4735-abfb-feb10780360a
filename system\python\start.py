import os
os.makedirs(os.path.join(os.path.dirname(__file__), "instance"), exist_ok=True)

from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from python.api.api import api_router
from service.models import Base, engine
from sqlalchemy_utils import database_exists, create_database
from sqlalchemy import create_engine
from contextlib import asynccontextmanager
from fastapi.staticfiles import StaticFiles

DATABASE_URL = "sqlite:///./instance/cramming.db"

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时
    engine = create_engine(DATABASE_URL)
    if not database_exists(engine.url):
        create_database(engine.url)
    Base.metadata.create_all(bind=engine)
    yield
    # 关闭时（如需清理资源可在这里加）

app = FastAPI(title="Cramming Knowledge Points API", lifespan=lifespan)

# 跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 路由
app.include_router(api_router)

# 静态文件托管
app.mount("/static", StaticFiles(directory=os.path.join(os.path.dirname(__file__), "templates")), name="static")

# 默认首页返回登录注册页面
@app.get("/", response_class=HTMLResponse)
def index():
    print("首页被访问")
    with open(os.path.join(os.path.dirname(__file__), "templates", "login.html"), encoding="utf-8") as f:
        return f.read()

if __name__ == "__main__":
    import uvicorn
    print("[INFO] FastAPI 服务启动中... 访问 http://127.0.0.1:8000/")
    uvicorn.run("start:app", host="0.0.0.0", port=8000, reload=True)
